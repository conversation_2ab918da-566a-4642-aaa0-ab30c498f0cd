<template>
  <div class="app-container">
    <!-- 查询条件表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
      <el-form-item label="业务类型" prop="businessTypeId">
        <el-select v-model="queryParams.businessTypeId" placeholder="请选择" clearable filterable >
          <el-option label="全部" value=""/>
          <el-option v-for="item in businessTypeOptions" :key="item.id" :value="item.id" :label="item.businessTypeName"/>
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择" clearable filterable>
          <el-option label="全部" value=""/>
          <el-option v-for="item in dictData.status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="负责项目经理" prop="projectManagers">
        <el-select v-model="queryParams.projectManagers" placeholder="请选择" clearable filterable>
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.projectManagers" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="成果类型" prop="resultType">
        <el-select v-model="queryParams.resultType" placeholder="请选择" clearable filterable>
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.types" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="优先级" prop="priorityLevel">
        <el-select v-model="queryParams.priorityLevel" placeholder="请选择" clearable filterable>
          <el-option label="全部" value=""/>
          <el-option v-for="item in dictData.priority" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="所属业务大类" prop="businessCategoryMajor">
        <el-select v-model="queryParams.businessCategoryMajor" placeholder="请选择" clearable filterable>
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.businessMajor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="所属业务小类" prop="businessCategoryMinor">
        <el-select v-model="queryParams.businessCategoryMinor" placeholder="请选择" clearable filterable>
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dictData.businessMinor" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>

      <el-form-item label="项目/任务" prop="projectTaskName">
        <el-input v-model="queryParams.projectTaskName" placeholder="请输入项目/任务名称" clearable/>
      </el-form-item>

      <el-form-item label="成果编码" prop="resultCode">
        <el-input v-model="queryParams.resultCode" placeholder="请输入成果编码" clearable />
      </el-form-item>

      <el-form-item label="创建时间" prop="createTimeRange">
        <el-date-picker
          v-model="queryParams.createTimeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="请选择时间"
          end-placeholder="请选择时间"
          style="width: 350px">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item>
        <span style="color: #999;">双击可查看成果关联的需求</span>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" @sort-change="handleSortChange" @row-dblclick="handleRowDblclick" height="calc(100vh - 300px)" stripe border>
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="业务类型" align="center" prop="businessTypeName" width="150" show-overflow-tooltip />
      <el-table-column label="成果编码" align="center" prop="resultCode" width="120" show-overflow-tooltip />
      <el-table-column label="成果类型" align="center" prop="resultType" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.resultType, dictData.types) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)" size="mini">
            {{ getDictLabel(scope.row.status, dictData.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="项目/任务名称" align="center" prop="projectTaskName" min-width="150" show-overflow-tooltip />

      <el-table-column label="优先级" align="center" prop="priorityLevel" width="100" sortable="custom">
        <template slot-scope="scope">
          <el-tag :type="getPriorityType(scope.row.priorityLevel)" size="mini">
            {{ scope.row.priorityLevel }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="项目里程碑" prop="milestoneDisplay" min-width="200">
        <template slot-scope="scope">
          <span>完成评审：{{ parseTime(scope.row.milestoneRequirements, '{y}-{m}-{d}') }}</span><br/>
          <span>完成开发：{{ parseTime(scope.row.milestoneDevelopment, '{y}-{m}-{d}') }}</span><br/>
          <span>完成测试验收：{{ parseTime(scope.row.milestoneTest, '{y}-{m}-{d}') }}</span><br/>
          <span>完成上线：{{ parseTime(scope.row.milestoneOnline, '{y}-{m}-{d}') }}</span><br/>
        </template>
      </el-table-column>

      <el-table-column label="任务说明/进度" prop="progressDisplay" min-width="180">
        <template slot-scope="scope">
          <span>需求评审：{{Number(scope.row.requirementsProgress)}}%</span><br/>
          <span>开发进度：{{Number(scope.row.developmentProgress)}}%</span><br/>
          <span>测试验收进度：{{Number(scope.row.testProgress)}}%</span><br/>
        </template>
      </el-table-column>

      <el-table-column label="干系人"  prop="stakeholderDisplay" min-width="180">
        <template slot-scope="scope">
          <span>产品：{{scope.row.productManagers}}</span><br/>
          <span>开发：{{convertDeptIdsToNames(scope.row.devTeams, "dev")}}</span><br/>
          <span>测试：{{convertDeptIdsToNames(scope.row.testTeams, "test")}}</span><br/>
        </template>
      </el-table-column>

      <el-table-column label="投入人力"  prop="manpowerDisplay" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.devManpower">开发：{{scope.row.devManpower}}人</span><br/>
          <span v-if="scope.row.testManpower">测试：{{scope.row.testManpower}}人</span>
        </template>
      </el-table-column>

      <el-table-column label="工作量(人日)" align="center" prop="workloadDisplay" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.devWorkload">开发：{{scope.row.devWorkload}}人日</span><br/>
          <span v-if="scope.row.testWorkload">测试：{{scope.row.testWorkload}}人日</span>
        </template>
      </el-table-column>

      <el-table-column label="需求背景" align="center" prop="requirementBackground" min-width="200">
        <template slot-scope="scope">
          <!-- <el-tooltip
            placement="top"
            :disabled="!scope.row.requirementBackground"
            popper-class="custom-tooltip"
            :max-width="400">
            <div slot="content" v-if="scope.row.requirementBackground" v-html="scope.row.requirementBackground"></div>
            <div slot="content" v-else>暂无需求背景</div> -->
            <div class="requirement-text-clickable">
              {{ getPlainText(scope.row.requirementBackground, 30) }}
            </div>
          <!-- </el-tooltip> -->
        </template>
      </el-table-column>

      <el-table-column label="负责项目经理" align="center" prop="projectManagers" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ convertProjectManagerIdsToNames(scope.row.projectManagers) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="更新时间" align="center" prop="updateTime" min-width="160" sortable="custom" />

      <el-table-column label="完成时间" align="center" prop="completionTime" min-width="120" sortable="custom">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completionTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="所属业务大类" align="center" prop="businessCategoryMajor" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMajor, dictData.businessMajor) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="所属业务小类" align="center" prop="businessCategoryMinor" width="100">
        <template slot-scope="scope">
          <span>{{ getDictLabel(scope.row.businessCategoryMinor, dictData.businessMinor) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="创建人" align="center" prop="createBy" min-width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="160" sortable="custom" />

      <el-table-column label="操作" align="center" width="180" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewDetail(scope.row)">查看详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleRemoveArchive(scope.row)" style="color: #e6a23c;">移除归档</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"/>

    <!-- 详情弹窗 -->
    <detailDialog
      :dialogVisible.sync="detailDialog.show"
      :dialogTitle="detailDialog.title"
      :recordId="detailDialog.recordId">
    </detailDialog>
  </div>
</template>

<script>
import { projectResultList, removeArchive} from "@/api/project/projectResult"
import detailDialog from "@/views/project/projectResult/components/detailDialog.vue"
import { parseTime } from "@/utils/ruoyi"
import { businessTypeList } from "@/api/project/businessType"

export default {
  name: "ArchivingProjectResult",
  dicts: [
    'project_outcome_types',
    'project_outcome_status',
    'project_outcome_priority_level',
    'project_outcome_business_category_major',
    'project_outcome_business_category_minor',
    'project_outcome_project_manager',
    'project_outcome_test_dept',
    'project_outcome_dev_dept'
  ],
  components: {
    detailDialog
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessTypeId: "",
        status: "",
        projectManagers: null,
        projectTaskName: "",
        resultType: null,
        businessCategoryMajor: null,
        businessCategoryMinor: null,
        priorityLevel: "",
        archiveFlag: 1,
        createTimeRange: []
      },
      // 详情弹窗配置
      detailDialog: {
        show: false,
        title: '项目成果详情',
        recordId: null
      },
      // 业务类型选项
      businessTypeOptions: []
    }
  },
  computed: {
    // 统一获取字典数据
    dictData() {
      return {
        types: this.dict.type.project_outcome_types || [],
        status: this.dict.type.project_outcome_status || [],
        priority: this.dict.type.project_outcome_priority_level || [],
        businessMajor: this.dict.type.project_outcome_business_category_major || [],
        businessMinor: this.dict.type.project_outcome_business_category_minor || [],
        projectManagers: this.dict.type.project_outcome_project_manager || [],
        devDepts: this.dict.type.project_outcome_dev_dept || [],
        testDepts: this.dict.type.project_outcome_test_dept || []
      }
    }
  },
  async created() {
    await this.loadOptions()
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      let params = { ...this.queryParams }
      if (this.queryParams.createTimeRange && this.queryParams.createTimeRange.length !== 0) {
        params.createTimeStart = this.queryParams.createTimeRange[0]
        params.createTimeEnd = this.queryParams.createTimeRange[1]
      }
      projectResultList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows || []
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      }).catch(() => {
        this.loading = false
      })
    },
    async loadOptions() {
      try {
        // 加载业务类型选项
        const res = await businessTypeList()
        if (res.code === 200) {
          this.businessTypeOptions = res.rows || []
        } else {
          console.warn('加载业务类型失败:', res.msg)
        }
      } catch (error) {
        console.error('加载业务类型失败:', error)
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm("queryForm")
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        businessTypeId: "",
        status: "",
        projectManagers: null,
        projectTaskName: "",
        resultType: null,
        businessCategoryMajor: null,
        businessCategoryMinor: null,
        priorityLevel: "",
        archiveFlag: 1,
        createTimeRange: []
      }
      this.handleQuery()
    },
    // 排序
    handleSortChange(column) {
      this.queryParams.orderByField = column.prop
      this.queryParams.orderRule = column.order === 'ascending' ? 'asc' : 'desc'
      this.handleQuery()
    },

    /** 查看详情 */
    handleViewDetail(row) {
      this.detailDialog.recordId = row.id
      this.detailDialog.title = `${row.projectTaskName} - 详情`
      this.detailDialog.show = true
    },

    /** 移除归档 */
    handleRemoveArchive(row) {
      this.$confirm('该数据将会变更为进行中状态并移到项目成果管理页面，是否确认？', '移除归档提示', {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await removeArchive(row.id)
          if (res.code === 200) {
            this.$message.success('移除归档成功')
            this.getList()
          } else {
            this.$message.error(res.msg || '移除归档失败')
          }
        } catch (error) {
          console.error('移除归档失败:', error)
        }
      }).catch(() => {
        // 用户点击取消按钮
        this.$message.info('已取消')
      })
    },
    // 获取部门名称
    convertDeptIdsToNames(deptIds, type) {
      if (!deptIds) return ''
      const ids = deptIds.split(',')
      const deptDict = type === 'dev' ? this.dictData.devDepts : this.dictData.testDepts
      if (!deptDict || !Array.isArray(deptDict)) return deptIds
      const names = ids.map(id => {
        const dept = deptDict.find(item => item.value === id.trim())
        return dept ? dept.label : id
      })
      return names.join('、')
    },
    /** 转换项目经理ID为名称 */
    convertProjectManagerIdsToNames(projectManagers) {
      if (!projectManagers) return ''
      const accounts = projectManagers.split(',')
      const managerDict = this.dictData.projectManagers
      if (!managerDict || !Array.isArray(managerDict)) return projectManagers
      const names = accounts.map(account => {
        const manager = managerDict.find(item => item.value === account.trim())
        return manager ? manager.label : account
      })
      return names.join(',')
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        '1': 'info',
        '2': 'warning',
        '3': 'success',
        '4': 'danger'
      }
      return statusMap[status] || 'info'
    },
    /** 获取优先级类型 */
    getPriorityType(priority) {
      const priorityMap = {
        'P1': 'danger',
        'P2': 'warning',
        'P3': 'success',
        'P4': 'info'
      }
      return priorityMap[priority] || 'info'
    },

    /** 获取字典标签 */
    getDictLabel(value, dictData) {
      if (!value || !dictData || !Array.isArray(dictData)) return '-'
      const item = dictData.find(dict => dict.value === value)
      return item ? item.label : value
    },

    /** 重置表单 */
    resetForm(formName) {
      this.$refs[formName] && this.$refs[formName].resetFields()
    },
    /** 获取纯文本内容并截断 */
    getPlainText(htmlContent, maxLength) {
      if (!htmlContent) return '';
      // 创建临时DOM元素来解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlContent
      // 获取纯文本内容
      const textContent = tempDiv.textContent || tempDiv.innerText || ''
      // 去除多余空格和换行
      const cleanText = textContent.replace(/\s+/g, ' ').trim()
      if (cleanText.length <= maxLength) {
        return cleanText
      }
      // 截断文本并添加省略号
      return cleanText.substring(0, maxLength) + '...'
    },
    /** 双击行跳转到需求页面 */
    handleRowDblclick(row) {
      if (!row.resultCode) {
        this.$message.warning('该项目成果无成果编码，无法跳转')
        return
      }
      // 跳转到需求页面
      this.$router.push({
        path: '/manage/demand',
        query: {
          resultCode: row.resultCode,
          highlight: 'true'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.requirement-detail-content {
  max-height: 400px;
  overflow-y: auto;
}

.requirement-text-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: inherit;
  line-height: 1.6;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}

// 分页样式
.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style>
