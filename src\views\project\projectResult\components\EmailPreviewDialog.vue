<template>
  <el-dialog
    title="邮件预览"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
  >
    <!-- 邮件内容预览 -->
    <div>
      <div class="email-header">
        <div>Dear</div>
        <div style="margin-top: 10px">
          下午好,以下是{{ currentMonth }}月技术中心重点支持的项目进展月报,整体无风险,请您查阅(也可查阅附件文件)。
        </div>
      </div>

      <!-- 项目进展表格 -->
      <el-table :data="projectDataWithIndex" border stripe style="width: 100%" v-loading="!projectData.length">
        <el-table-column prop="index" label="序号" width="60" align="center" />
        <el-table-column prop="projectTaskName" label="项目/任务名称"  align="center" show-overflow-tooltip />
        <el-table-column prop="businessCategoryMajor" label="业务大类"  align="center">
          <template #default="{ row }">
            <span>{{ getDictLabel(row.businessCategoryMajor, dictData['project_outcome_business_category_major']) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="businessCategoryMinor" label="业务小类" align="center">
          <template #default="{ row }">
            <span>{{ getDictLabel(row.businessCategoryMinor, dictData['project_outcome_business_category_minor']) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="productManagers" label="产品负责人" align="center"  show-overflow-tooltip />
        <el-table-column prop="businessManager" label="业务负责人" align="center"  show-overflow-tooltip />
        <el-table-column prop="milestoneDisplay" label="项目里程碑"   align="center">
          <template #default="{ row }">
            <div class="milestone-content">完成上线：{{row.milestoneOnline ? parseTime(row.milestoneOnline, '{y}-{m}-{d}') : '--'}}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSend" :loading="sending">{{ sending ? '发送中...' : '发送' }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { sendProjectResultEmail, selectProjectResultsByIds } from "@/api/project/projectResult"

export default {
  name: 'EmailPreviewDialog',
  props: {
    visible: { type: Boolean, default: false },
    selectedProjects: { type: Array, default: () => [] },
    dictData: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      sending: false,
      projectData: []
    }
  },
  computed: {
    currentMonth() {
      const now = new Date()
      return `${now.getMonth() + 1}`
    },
    projectDataWithIndex() {
      return this.projectData.map((row, index) => ({
        ...row,
        index: index + 1
      }))
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val && this.selectedProjects.length > 0) {
          this.fetchProjectData()
        } else if (!val) {
          this.projectData = []
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 获取项目数据 */
    async fetchProjectData() {
      try {
        const ids = this.selectedProjects.map(project => project.id)
        const res = await selectProjectResultsByIds(ids)
        if (res.code === 200) {
          this.projectData = res.data || []
        } else {
          this.$message.error('获取项目数据失败')
          this.projectData = []
        }
      } catch (error) {
        console.error('获取项目数据失败:', error)
        this.projectData = []
      }
    },
    /** 获取字典标签 */
    getDictLabel(value, dictData) {
      if (!dictData || value === undefined || value === null || value === '') return value || '-'
      const item = (dictData || []).find(d => String(d.value) === String(value))
      return item ? item.label : value
    },
    /** 取消发送 */
    handleCancel() {
      this.$emit('update:visible', false)
      this.$emit('closed')
    },

    /** 弹窗关闭处理 */
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('closed')
    },
    /** 发送邮件 */
    async handleSend() {
      const resultIds = this.selectedProjects.map(project => project.id)
      console.log('发送邮件的项目ID列表:', resultIds)
      if (resultIds.length === 0) {
        this.$message.warning('没有项目数据可发送')
        return
      }
      this.sending = true
      try {
        const res = await sendProjectResultEmail(resultIds)
        if (res.code === 200) {
          this.$message.success('邮件发送成功')
          this.$emit('update:visible', false)
          this.$emit('sent')
        } else {
          this.$message.error(res.msg || '邮件发送失败')
        }
      } catch (error) {
        console.error('发送邮件失败:', error)
      } finally {
        this.sending = false
      }
    }
  }
}
</script>
<style scoped>
.email-header {
  margin-bottom: 20px;
  font-weight: bold;
}

.milestone-content {
  line-height: 1.5;
  text-align: center;
}
</style>
